.main_nav_wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
}

.main_nav_wrapper {
  transition: width 0.5s ease;
}

.main_nav-nav_links {
  transition: all 0.3s ease;
  position: relative;
}

.dropdown_opened::after {
  position: absolute;
  content: '';
  width: 100%;
  /* max-width: 220px; */
  height: 100%;
  background-color: rgba(var(--color-white), 0.9);
  top: 0;
  left: 0;
  border-radius: 4px;
}

.main_nav_wrapper .rotate-180 {
  transform: rotate(180deg);
}

/* Special animation for navigation text items */
.animated-nav-text {
  transition-property: transform, opacity, width;
  transition-duration: 800ms;
  transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: left center;
  opacity: 1;
  overflow: hidden;
}

/* Animation for each nav item with different delays */
.main_nav_wrapper:not(.w-20) .animated-nav-text {
  animation: textAppear 800ms cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  animation-fill-mode: both;
}

/* All navigation items - same timing */
.main_nav_wrapper:not(.w-20) .animated-nav-text {
  animation-delay: 0ms;
}

/* Closing animation for animated nav text */
.main_nav_wrapper.w-20 .animated-nav-text {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
}

/* Human Handover animation */
.main_nav_wrapper:not(.w-20) .human-handover-nav-item .nav-label {
  animation: textAppear 800ms cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  animation-fill-mode: both;
  animation-delay: 0ms;
  transition-property: transform, opacity, width;
  transition-duration: 800ms;
  transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: left center;
  opacity: 1;
  overflow: hidden;
}

/* Closing animation for Human Handover */
.main_nav_wrapper.w-20 .human-handover-nav-item .nav-label {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
}

@keyframes textAppear {
  0% {
    opacity: 0;
    transform: translateX(-15px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation for sidebar usage section */
.main_nav_wrapper:not(.w-20) .sidebar-usage-section > div {
  animation: usageAppear 500ms cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  opacity: 0;
}

/* Same delay for all usage sections */
.main_nav_wrapper:not(.w-20) .sidebar-usage-section > div {
  animation-delay: 150ms;
}

@keyframes usageAppear {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (min-width: 640px) {
  .main_nav_wrapper.collapsed {
    width: 80px;
  }
}
